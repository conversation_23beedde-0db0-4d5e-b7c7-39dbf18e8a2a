// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
//  await Firebase.initializeApp(
//    options: DefaultFirebaseOptions.currentPlatform,
//  );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAIzpvSRiYrh22syXrxV4EqoYbGBfQVsBM',
    appId: '1:500896448263:web:8a78ebcb8b343f04268fa2',
    messagingSenderId: '500896448263',
    projectId: 'qadam-app',
    authDomain: 'qadam-app.firebaseapp.com',
    storageBucket: 'qadam-app.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBbL4adiuXwqfMIbcqCER4bgCssD5zaTlY',
    appId: '1:500896448263:android:350c12cb273c93f2268fa2',
    messagingSenderId: '500896448263',
    projectId: 'qadam-app',
    storageBucket: 'qadam-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDY8C2LgNevzep68YDgMmUWklQeWzj7wbo',
    appId: '1:500896448263:ios:57487a7b1e0cdf4e268fa2',
    messagingSenderId: '500896448263',
    projectId: 'qadam-app',
    storageBucket: 'qadam-app.firebasestorage.app',
    iosBundleId: 'com.example.qadamApp',
  );
}
